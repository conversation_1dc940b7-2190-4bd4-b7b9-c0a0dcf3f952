<?php
session_start();
require '../core/dbcon.ini';
require 'query/students.qry';
require 'classes/os_browser.php';

date_default_timezone_set('Asia/Manila');
$current_date = date("Y-m-d");

// Initialize class
$students = new STUDENTS();

// Get device info
$ip_address = getenv("REMOTE_ADDR");
$user_os = getOS();
$user_browser = getBrowser();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db1->beginTransaction();

        $student_id = $_POST['student_id'];
        $os = $_POST['os'];
        $browser = $_POST['browser'];

        // Check if student exists in cdas_student table
        if (!$students->check_student_exists($db1, $student_id)) {
            throw new Exception("Student ID not found. Please check if you're officially enrolled.");
        }

        // Check if student ID is already registered with a device
        if ($students->check_student_device_registered($db1, $student_id)) {
            throw new Exception("Student ID is already registered with another device. Only one device per student is allowed.");
        }

        // Register device for this student
        if ($students->register_device($db1, $student_id, $ip_address, $os, $browser)) {
            $db1->commit();
            $message = "Device successfully registered for Student ID: $student_id. IP Address: $ip_address";
            echo "<script>
                alert('$message');
                window.location.href = 'attendance.php';
            </script>";
            exit();
        } else {
            throw new Exception("Failed to register device. Please try again.");
        }

    } catch(Exception $e) {
        $db1->rollBack();
        echo "<script>
            alert('". addslashes($e->getMessage()) ."'); 
            window.history.back();
        </script>";
        exit();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <title>Don Bosco College - Device Registration</title>
    <link rel="stylesheet" href="css/register.css">
</head>
<body>
    <div class="register-container">
        <h1>Device Registration</h1>
        <p>Register your device to access the attendance system</p>

        <div class="info-box">
            <h3>Device Information</h3>
            <p><strong>IP Address:</strong> <?php echo $ip_address; ?></p>
            <p><strong>Operating System:</strong> <?php echo $user_os; ?></p>
            <p><strong>Browser:</strong> <?php echo $user_browser; ?></p>
        </div>

        <form method="post">
            <div class="form-group">
                <label for="student_id">Student ID</label>
                <input type="text" id="student_id" name="student_id" placeholder="Enter your 6-digit Student ID" required>
                <div class="input-guide">
                    Enter your 6-digit Student ID (e.g., 123456).
                    <span class="view-sample-link" onclick="showSampleModal()">view sample</span>
                </div>
            </div>

            <input type="hidden" name="os" value="<?php echo $user_os; ?>">
            <input type="hidden" name="browser" value="<?php echo $user_browser; ?>">

            <button type="submit">
                Register Device
            </button>
        </form>

        <p class="footer-text">
            Student ID not working? Check if you're officially enrolled. or ask the registrar office for assistance.
        </p>

        <a href="attendance.php" class="back-link">
            ← Back to Attendance
        </a>
    </div>

    <!-- Sample ID Modal -->
    <div id="sampleModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeSampleModal()">&times;</span>
            <h3>Where to Find Your Student ID</h3>
            <div class="sample-image-container">
                <img src="assets/sampleid.png" alt="Student ID Sample" class="sample-image">
                <p class="sample-description">
                    Your Student ID can be found on your student identification card as shown above.
                    Enter the complete ID number exactly as it appears.
                </p>
            </div>
        </div>
    </div>

    <script>
        function showSampleModal() {
            document.getElementById('sampleModal').style.display = 'block';
        }

        function closeSampleModal() {
            document.getElementById('sampleModal').style.display = 'none';
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('sampleModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
